import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Check, ChevronDown, Clock, ChevronLeft, ChevronRight, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import React, { useState } from 'react';

interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
}

interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  steps: Step[];
}

interface SidebarContentProps {
  stages: Stage[];
  currentStage: number;
  currentStep: number;
  expandedStages: number[];
  stepStatus: { [key: string]: boolean };
  progress: number;
  completedSteps: number;
  totalSteps: number;
  toggleStageExpansion: (stageId: number) => void;
  goToStep: (stageId: number, stepIndex: number) => void;
  isMobileMenuOpen?: boolean;
  className?: string;
  onCollapsedChange?: (collapsed: boolean) => void;
  initialCollapsed?: boolean;
  closeMobileMenu?: () => void; // New prop for direct mobile menu closing
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  stages,
  currentStage,
  currentStep,
  expandedStages,
  stepStatus,
  progress,
  completedSteps,
  totalSteps,
  toggleStageExpansion,
  goToStep,
  isMobileMenuOpen = false,
  className,
  onCollapsedChange,
  initialCollapsed = false,
  closeMobileMenu // Destructure the new prop
}) => {
  // Use local state for each sidebar's collapsed state instead of global context
  const [sidebarCollapsed, setSidebarCollapsed] = useState(initialCollapsed);
  
  // Notify parent component when collapse state changes
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    if (onCollapsedChange) {
      onCollapsedChange(newState);
    }
  };
  
  // Function to handle mobile sidebar backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Prevent event propagation
    e.stopPropagation();
    
    // Only handle if mobile menu is open
    if (isMobileMenuOpen) {
      if (closeMobileMenu) {
        closeMobileMenu(); // Use the new prop if available
      } else if (onCollapsedChange) {
        onCollapsedChange(true); // Fallback to existing behavior
      }
    }
  };

  return (
    <TooltipProvider>
      {/* Mobile backdrop - only shown on mobile */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/40 md:hidden"
          onClick={handleBackdropClick}
          aria-label="Close sidebar backdrop"
        />
      )}
      <aside
        className={cn(
          'fixed top-14 left-0 bottom-0 z-50 shrink-0 border-r border-border/40 bg-background shadow-sm',
          'transition-all duration-300 ease-in-out flex flex-col',
          'md:static md:mt-0 md:top-0 md:translate-x-0 md:h-full md:z-0 md:shadow-none',
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0',
          isMobileMenuOpen ? 'w-80' : sidebarCollapsed ? 'w-16' : 'w-64',
          className
        )}
        style={{
          minWidth: isMobileMenuOpen ? 320 : (sidebarCollapsed ? 64 : 256),
          height: 'calc(100vh - 56px)', // 56px is the default navbar height
          maxHeight: 'calc(100vh - 56px)' // Ensure it doesn't exceed viewport height
        }}
      >
        {/* Collapse/Expand Button (desktop only) */}
        {/* Mobile close button - only visible on mobile */}
        {isMobileMenuOpen && (
          <div className="flex justify-end p-2 md:hidden">
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-8 w-8" 
              onClick={handleBackdropClick}
              aria-label="Close sidebar"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {/* Desktop collapse/expand button - only visible on desktop */}
        <button
          className={cn(
            'absolute top-4 -right-3 z-30 bg-background border border-border/60 rounded-full p-1.5 shadow-md',
            'transition-all duration-200 hover:shadow-lg hidden md:flex md:items-center md:justify-center',
            'transform hover:scale-105 active:scale-95'
          )}
          style={{ right: '-16px' }}
          onClick={toggleSidebar}
          aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
        </button>
        
        <ScrollArea className="flex-1">
          <div className={cn("px-3 py-4 lg:p-4 space-y-3", sidebarCollapsed ? "px-2" : "")}>
            {stages.map(stage => {
              const isCurrentStage = stage.id === currentStage;
              const isExpanded = expandedStages.includes(stage.id);
              const StageIcon = stage.icon;
              const stageCompleted = stage.steps.every(step => stepStatus[step.id]);

              return (
                <div key={stage.id} className="w-full group">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={isCurrentStage ? 'secondary' : 'ghost'}
                        className={cn(
                          'w-full h-auto py-2.5 px-3 text-left relative rounded-lg transition-all duration-200',
                          isCurrentStage ? 'bg-secondary/50 shadow-sm' : 'hover:bg-muted/60',
                          stageCompleted ? 'text-purple-700 font-medium' : '',
                          sidebarCollapsed ? 'justify-center px-1' : ''
                        )}
                        onClick={() => toggleStageExpansion(stage.id)}
                      >
                        <div className={cn(
                          "flex items-center gap-2 min-w-0 w-full", 
                          !sidebarCollapsed ? "pr-6" : ""
                        )}>
                          <div
                            className={cn(
                              'w-6 h-6 lg:w-7 lg:h-7 rounded-full flex items-center justify-center flex-shrink-0 transition-colors duration-200',
                              stageCompleted
                                ? 'bg-purple-100 text-purple-700'
                                : isCurrentStage
                                  ? 'bg-secondary-foreground/10 text-foreground'
                                  : 'bg-muted text-muted-foreground group-hover:bg-muted/80'
                            )}
                          >
                            {stageCompleted ? (
                              <Check className="h-3 w-3 lg:h-3.5 lg:w-3.5" />
                            ) : (
                              <StageIcon className="h-3 w-3 lg:h-3.5 lg:w-3.5" />
                            )}
                          </div>
                          {!sidebarCollapsed && (
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-xs lg:text-sm truncate">{stage.title}</div>
                              <div className="text-xs text-muted-foreground hidden lg:block truncate">
                                {stage.description}
                              </div>
                            </div>
                          )}
                        </div>
                        {!sidebarCollapsed && (
                          <ChevronDown
                            className={cn(
                              'h-3 w-3 lg:h-4 lg:w-4 transition-transform absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground',
                              isExpanded ? 'rotate-180' : ''
                            )}
                          />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="max-w-xs hidden lg:block">
                      <div>
                        <div className="font-medium">{stage.title}</div>
                        <div className="text-xs text-muted-foreground">{stage.description}</div>
                      </div>
                    </TooltipContent>
                  </Tooltip>

                  {isExpanded && !sidebarCollapsed && (
                    <div className="ml-6 lg:ml-8 pl-3 border-l border-primary/10 mt-1.5 mb-2 space-y-1.5">
                      {stage.steps.map((step, stepIndex) => {
                        const StepIcon = step.icon;
                        const isCurrentStep = isCurrentStage && stepIndex === currentStep;
                        const isStepCompleted = stepStatus[step.id];

                        return (
                          <Tooltip key={step.id}>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className={cn(
                                  'w-full justify-start h-auto py-1.5 px-2.5 text-left rounded-md transition-all duration-150',
                                  isCurrentStep ? 'bg-secondary/50 shadow-sm' : 'hover:bg-muted/60',
                                  isStepCompleted ? 'text-purple-700 font-medium' : ''
                                )}
                                onClick={() => goToStep(stage.id, stepIndex)}
                              >
                                <div className="flex items-center gap-2 w-full min-w-0">
                                  <div
                                    className={cn(
                                      'w-4 h-4 lg:w-5 lg:h-5 rounded-full flex items-center justify-center flex-shrink-0 transition-colors',
                                      isStepCompleted ? 'bg-purple-100 text-purple-700' : 'bg-muted/70 text-muted-foreground'
                                    )}
                                  >
                                    {isStepCompleted ? (
                                      <Check className="h-2 w-2 lg:h-2.5 lg:w-2.5" />
                                    ) : (
                                      <StepIcon className="h-2 w-2 lg:h-2.5 lg:w-2.5" />
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="text-xs font-medium truncate">{step.title}</div>
                                  </div>
                                  {isCurrentStep && <Clock className="h-2.5 w-2.5 lg:h-3 lg:w-3 flex-shrink-0 text-foreground/80" />}
                                </div>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="right" className="max-w-xs hidden lg:block">
                              <div>
                                <div className="font-medium">{step.title}</div>
                                <div className="text-xs text-muted-foreground">{step.description}</div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>
        
        {/* Progress indicator in sidebar */}
        {!sidebarCollapsed ? (
          <div className="border-t border-border/30 p-3 lg:p-4 bg-background flex-shrink-0 shadow-inner">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs lg:text-sm">
                <span className="font-medium truncate">Progress</span>
                <span className="text-muted-foreground font-semibold flex-shrink-0 ml-2">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-1.5 lg:h-2" />
              <div className="text-xs text-muted-foreground truncate">
                {completedSteps} of {totalSteps} completed
              </div>
            </div>
          </div>
        ) : (
          <div className="border-t border-border/30 p-2 bg-background flex-shrink-0 shadow-inner">
            <div className="flex flex-col items-center justify-center py-2">
              <Progress value={progress} className="h-1.5 w-8 mb-1" />
              <span className="text-xs font-medium">{Math.round(progress)}%</span>
            </div>
          </div>
        )}
      </aside>
    </TooltipProvider>
  );
};

export default SidebarContent;
